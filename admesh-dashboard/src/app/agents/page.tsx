"use client";

import { useRouter } from "next/navigation";
import AgentContent from "@/components/AgentContent";

import Footer from "@/components/Footer";

type Role = "user" | "brand" | "agent" | null;

export default function AgentsPage() {
  const router = useRouter();

  const handleRoleChange = (role: Role) => {
    if (role === "user") {
      router.push("/users");
    } else if (role === "brand") {
      router.push("/");
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <AgentContent onRoleChange={handleRoleChange} />

      {/* Floating Role Switcher */}
      {/* <FloatingRoleSwitcher
        currentRole="agent"
        onRoleChange={handleRoleChange}
      /> */}

      <Footer />
    </div>
  );
}
